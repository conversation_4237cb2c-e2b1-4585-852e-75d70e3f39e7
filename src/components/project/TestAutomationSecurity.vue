<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';
import { TestCase } from '../../types';
import { TestWebSocketClient } from '../../utils/testWebSocketClient';

interface TestExecutionRequest {
  testCaseId: string;
  tcId: string;
  projectId: string;
  steps: any[];
  testCase: {
    title: string;
    precondition?: string;
    expectation?: string;
  };
  clientId?: string;
}

const route = useRoute();
const projectId = route.params.id as string;
const tcId = route.params.tcId as string;
const selectedTestCase = ref<TestCase | null>(null);
const automationSteps = ref<any[]>([]);
const isEditing = ref(false);

// Test execution state for logs display
const testProgress = ref({
  status: 'idle',
  logs: [] as string[],
  error: '',
  message: '',
  currentStep: 0,
  totalSteps: 0
});

// AgentQ API key state
const agentqApiKey = ref<string | null>(null);
const testRunning = ref(false);
const isRunning = ref(false);
const queueStatus = ref({ active: 0, waiting: 0 });

// WebSocket client and polling variables
let wsClient: any = null;
let currentClientId: string | null = null;
let pollingInterval: NodeJS.Timeout | null = null;

// Active tab state for the execution panel
const activeTab = ref('logs');

// Function to parse steps from the test case response
const parseStepsFromTestCase = (testCase: TestCase) => {
  const steps: any[] = [];
  
  // Add precondition as the first step if it exists
  if (testCase.precondition) {
    steps.push({
      step: 1,
      stepName: testCase.precondition.trim()
    });
  }
  
  // Add the rest of the steps
  if (testCase.steps) {
    // Map each line to an automation step
    testCase.steps.split('\n').forEach(step => {
      // Default values
      const stepObj: any = {
        step: steps.length + 1, // Start numbering after the precondition step
        stepName: step.trim()
      };
      
      steps.push(stepObj);
    });
  }
  
  // Add expectation as the last step if it exists
  if (testCase.expectation) {
    const stepObj: any = {
      step: steps.length + 1,
      stepName: testCase.expectation.trim()
    };
    
    steps.push(stepObj);
  }
  
  return steps;
};

// Function to change the active tab
const setActiveTab = (tab: string) => {
  activeTab.value = tab;
};

// Function to fetch AgentQ API key from backend
const fetchAgentQApiKey = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/api-keys`
    );

    if (response.data && response.data.length > 0) {
      // Find the AgentQ API key
      const agentqKey = response.data.find((key: any) => key.provider === 'agentq');
      if (agentqKey) {
        agentqApiKey.value = agentqKey.apiKey;
        console.log('AgentQ API key fetched successfully');
        return agentqKey.apiKey;
      } else {
        console.error('No AgentQ API key found');
        testProgress.value.logs.push('⚠️ No AgentQ API key found in backend');
        return null;
      }
    } else {
      console.error('No API keys found');
      testProgress.value.logs.push('⚠️ No API keys configured in backend');
      return null;
    }
  } catch (error) {
    console.error('Failed to fetch AgentQ API key:', error);
    testProgress.value.logs.push('❌ Failed to fetch AgentQ API key from backend');
    return null;
  }
};

const fetchAutomationSteps = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/automation`
    );

    if (response.data && response.data.steps && response.data.steps.length > 0) {
      // Use the saved automation steps
      automationSteps.value = response.data.steps;
      console.log('Loaded saved automation steps:', automationSteps.value);
    } else if (selectedTestCase.value) {
      // Generate steps from the test case if no saved automation exists
      automationSteps.value = parseStepsFromTestCase(selectedTestCase.value);
      console.log('Generated automation steps from test case:', automationSteps.value);
    }
  } catch (error) {
    console.error('Failed to fetch automation steps:', error);
    // Fall back to generating steps from the test case
    if (selectedTestCase.value) {
      automationSteps.value = parseStepsFromTestCase(selectedTestCase.value);
    }
  }
};

const fetchTestCaseDetails = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}`
    );

    if (response.data) {
      selectedTestCase.value = response.data;
      console.log('Found test case:', response.data);

      // Fetch automation steps after getting the test case
      await fetchAutomationSteps();
    } else {
      console.error('Test case not found with tcId:', tcId);
    }
  } catch (error) {
    console.error('Failed to fetch test case details:', error);
  }
};

// Utility functions
const generateUniqueClientId = () => {
  return `client_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

const checkQueueStatus = async () => {
  try {
    const wsServerUrl = (import.meta as any).env.VITE_WEBSOCKET_SECURITY_URL?.replace('wss://', 'https://').replace('ws://', 'http://') || 'http://localhost:3023';
    const response = await axios.get(`${wsServerUrl}/api/queue/stats`);

    if (response.data && response.data.success) {
      const stats = response.data.data;
      queueStatus.value = stats;
      return stats;
    }
  } catch (error) {
    console.error('Failed to check queue status:', error);
  }
  return null;
};

const checkTestCaseRunning = async (testCaseId: string) => {
  try {
    const wsServerUrl = (import.meta as any).env.VITE_WEBSOCKET_SECURITY_URL?.replace('wss://', 'https://').replace('ws://', 'http://') || 'http://localhost:3023';
    const response = await axios.get(`${wsServerUrl}/api/queue/running-test-cases`);

    if (response.data && response.data.success) {
      const runningTestCases = response.data.data || [];
      return runningTestCases.includes(testCaseId);
    }
  } catch (error) {
    console.error('Failed to check running test cases:', error);
  }
  return false;
};

const addLogEntry = (message: string) => {
  const cleanedMessage = cleanLogForDisplay(message);
  testProgress.value.logs.push(cleanedMessage);
};

const cleanLogForDisplay = (message: string) => {
  if (!message) return '';

  // Remove ANSI escape codes
  let cleaned = message.replace(/\x1b\[[0-9;]*m/g, '');

  // Remove excessive whitespace
  cleaned = cleaned.replace(/\s+/g, ' ').trim();

  return cleaned;
};

const startTestCompletionPolling = (clientId: string) => {
  if (pollingInterval) {
    clearInterval(pollingInterval);
  }

  console.log('🔄 Starting immediate test completion polling for:', clientId);

  let pollCount = 0;
  const maxPolls = 24; // Maximum 2 minutes of polling (24 * 5 seconds)

  // Immediate first check
  checkTestCompletion();

  pollingInterval = setInterval(async () => {
    await checkTestCompletion();
  }, 5000); // Poll every 5 seconds

  async function checkTestCompletion() {
    pollCount++;

    try {
      console.log(`� Checking test completion (attempt ${pollCount}/${maxPolls})...`);

      // Primary: Check database for recent results
      try {
        const backendResponse = await axios.get(
          `${(import.meta as any).env.VITE_BACKEND_URL}/temp-test-results/test-case/${selectedTestCase.value?.id}`
        );

        if (backendResponse.data && backendResponse.data.length > 0) {
          const latestResult = backendResponse.data[0];
          const resultTime = new Date(latestResult.createdAt).getTime();
          const testStartTime = new Date().getTime() - 300000; // 5 minutes ago

          // If we have a recent result, the test completed
          if (resultTime > testStartTime) {
            console.log('✅ Found recent test completion:', latestResult.status);

            stopTestCompletionPolling();
            testRunning.value = false;
            isRunning.value = false;

            testProgress.value.status = latestResult.status;
            testProgress.value.message = latestResult.status === 'passed' ? 'Test completed successfully' : 'Test completed';

            const completionMessage = latestResult.status === 'passed' ? '✅ Test completed successfully' : '⚠️ Test completed with issues';
            testProgress.value.logs.push(completionMessage);

            // ALWAYS generate ZAP security report for security tests (regardless of pass/fail)
            testProgress.value.logs.push('📊 Generating ZAP security report...');
            testProgress.value.logs.push('🔒 Security analysis is independent of test result');

            setTimeout(() => {
              fetchZapReport();
            }, 2000);

            // Merge detailed logs
            await mergeDetailedLogs();
            return true; // Completion found
          }
        }
      } catch (dbError) {
        console.error('Error checking test completion:', dbError);
      }

      // Secondary: Check queue stats
      try {
        const wsServerUrl = (import.meta as any).env.VITE_WEBSOCKET_SECURITY_URL?.replace('wss://', 'https://').replace('ws://', 'http://') || 'http://localhost:3023';
        const response = await axios.get(`${wsServerUrl}/api/queue/stats`);

        if (response.data && response.data.success) {
          const stats = response.data.data;
          console.log('📊 Queue stats:', stats);

          // If queue shows no active jobs, force completion check
          if (stats.active === 0 && (testProgress.value.status === 'running' || testProgress.value.status === 'queued')) {
            console.log('📊 Queue shows no active jobs, forcing ZAP report generation...');

            stopTestCompletionPolling();
            testRunning.value = false;
            isRunning.value = false;

            testProgress.value.status = 'completed';
            testProgress.value.message = 'Test execution completed';
            testProgress.value.logs.push('📊 Test execution detected as complete');
            testProgress.value.logs.push('📊 Generating ZAP security report...');
            testProgress.value.logs.push('🔒 Security analysis is independent of test result');

            setTimeout(() => {
              fetchZapReport();
            }, 2000);

            await mergeDetailedLogs();
            return true; // Completion forced
          }
        }
      } catch (queueError) {
        console.error('Error checking queue stats:', queueError);
      }

      // Timeout after maximum attempts
      if (pollCount >= maxPolls) {
        console.log('⏰ Maximum polling attempts reached, forcing completion...');
        stopTestCompletionPolling();
        testRunning.value = false;
        isRunning.value = false;

        testProgress.value.status = 'completed';
        testProgress.value.message = 'Test execution completed';
        testProgress.value.logs.push('⏰ Test execution timeout reached');
        testProgress.value.logs.push('📊 Generating ZAP security report...');
        testProgress.value.logs.push('🔒 Security analysis will proceed regardless');

        setTimeout(() => {
          fetchZapReport();
        }, 2000);

        await mergeDetailedLogs();
        return true; // Timeout reached
      }

      return false; // Continue polling

    } catch (error) {
      console.error('Polling error:', error);
      return false;
    }
  }
};

const stopTestCompletionPolling = () => {
  if (pollingInterval) {
    clearInterval(pollingInterval);
    pollingInterval = null;
  }
};

const forceStopTest = () => {
  console.log('🛑 Force stopping security test execution');
  testProgress.value.logs.push('🛑 Security test execution manually stopped');
  testProgress.value.status = 'failed';
  testProgress.value.message = 'Security test execution stopped by user';
  testRunning.value = false;
  isRunning.value = false;

  if (wsClient) {
    wsClient.disconnect();
  }

  stopTestCompletionPolling();
};

const mergeDetailedLogs = async () => {
  try {
    // Fetch detailed logs from the backend
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/test-results`
    );

    if (response.data && response.data.length > 0) {
      const latestResult = response.data[0];
      if (latestResult.logs) {
        // Parse and merge the detailed logs
        const detailedLogs = JSON.parse(latestResult.logs);
        if (Array.isArray(detailedLogs) && detailedLogs.length > 0) {
          testProgress.value.logs.push('📋 Detailed execution logs:');
          detailedLogs.forEach(log => {
            if (log && typeof log === 'string') {
              addLogEntry(log);
            }
          });
        }
      }
    }
  } catch (error) {
    console.error('Failed to merge detailed logs:', error);
  }
};

const clearLogs = () => {
  testProgress.value.logs = [];
};

const fetchZapReport = async () => {
  try {
    testProgress.value.logs.push('🔍 Fetching ZAP security report...');

    // Fetch ZAP report through backend API to avoid CORS issues
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/security/zap-report`,
      {
        params: {
          testCaseId: selectedTestCase.value?.id
        }
      }
    );

    if (response.data && response.data.success) {
      const reportData = response.data.data;

      // Check if this is a stored report
      if (reportData.stored) {
        testProgress.value.logs.push('📋 Stored Security Report Loaded');
        testProgress.value.logs.push(`🕒 Report from: ${new Date(reportData.timestamp).toLocaleString()}`);
        testProgress.value.logs.push('💡 This is from a previous security scan');
      } else {
        testProgress.value.logs.push('📋 Fresh ZAP Security Report Generated');
      }

      // Display scan context information (URLs scanned)
      if (reportData.scanContext) {
        testProgress.value.logs.push(`🎯 Scan Target Information:`);

        if (reportData.scanContext.targetUrl) {
          testProgress.value.logs.push(`🌐 Primary Target: ${reportData.scanContext.targetUrl}`);
        }

        if (reportData.scanContext.sites && reportData.scanContext.sites.length > 0) {
          testProgress.value.logs.push(`📍 Sites Scanned: ${reportData.scanContext.sites.length}`);
          reportData.scanContext.sites.slice(0, 10).forEach((site: string, index: number) => {
            testProgress.value.logs.push(`   ${index + 1}. ${site}`);
          });
          if (reportData.scanContext.sites.length > 10) {
            testProgress.value.logs.push(`   ... and ${reportData.scanContext.sites.length - 10} more sites`);
          }
        }

        if (reportData.scanContext.urls && reportData.scanContext.urls.length > 0) {
          testProgress.value.logs.push(`🔗 URLs Scanned: ${reportData.scanContext.urls.length} total`);
          reportData.scanContext.urls.slice(0, 5).forEach((url: string, index: number) => {
            testProgress.value.logs.push(`   ${index + 1}. ${url}`);
          });
          if (reportData.scanContext.urls.length > 5) {
            testProgress.value.logs.push(`   ... and ${reportData.scanContext.urls.length - 5} more URLs`);
          }
        }

        if (reportData.scanContext.hosts && reportData.scanContext.hosts.length > 0) {
          testProgress.value.logs.push(`🌐 Hosts Contacted: ${reportData.scanContext.hosts.join(', ')}`);
        }
      }

      // Display report summary
      if (reportData.summary) {
        // Check if we have detailed vulnerability summary
        if (reportData.summary.highRisk !== undefined || reportData.summary.totalIssues !== undefined) {
          testProgress.value.logs.push(`📊 Security Scan Summary:`);
          testProgress.value.logs.push(`🔴 High Risk: ${reportData.summary.highRisk || 0} issues`);
          testProgress.value.logs.push(`🟡 Medium Risk: ${reportData.summary.mediumRisk || 0} issues`);
          testProgress.value.logs.push(`🟢 Low Risk: ${reportData.summary.lowRisk || 0} issues`);
          testProgress.value.logs.push(`ℹ️ Informational: ${reportData.summary.informational || 0} issues`);
          testProgress.value.logs.push(`📈 Total Issues: ${reportData.summary.totalIssues || 0}`);

          // Display URLs scanned count from summary if available
          if (reportData.summary.urlsScanned !== undefined) {
            testProgress.value.logs.push(`🔍 URLs Analyzed: ${reportData.summary.urlsScanned}`);
          }
        } else if (reportData.summary.message) {
          // Handle stored report with message only
          testProgress.value.logs.push(`📊 ${reportData.summary.message}`);
          if (reportData.summary.storedAt) {
            testProgress.value.logs.push(`🕒 Report stored at: ${new Date(reportData.summary.storedAt).toLocaleString()}`);
          }
          testProgress.value.logs.push(`💡 Detailed vulnerability data not available for stored reports`);
          testProgress.value.logs.push(`🔄 Run a new security test to get detailed vulnerability analysis`);
        }
      }

      // Display individual vulnerabilities
      if (reportData.vulnerabilities && reportData.vulnerabilities.length > 0) {
        testProgress.value.logs.push('🔍 Vulnerability Details:');
        reportData.vulnerabilities.slice(0, 10).forEach((vuln: any) => {
          const riskIcon = vuln.risk === 'High' ? '🔴' : vuln.risk === 'Medium' ? '🟡' : vuln.risk === 'Low' ? '🟢' : 'ℹ️';
          testProgress.value.logs.push(`${riskIcon} ${vuln.name} (${vuln.risk})`);
        });

        if (reportData.vulnerabilities.length > 10) {
          testProgress.value.logs.push(`... and ${reportData.vulnerabilities.length - 10} more issues`);
        }
      } else if (reportData.stored) {
        // For stored reports without detailed vulnerability data
        testProgress.value.logs.push('📋 Stored security report loaded successfully');
        testProgress.value.logs.push('💡 Detailed vulnerability breakdown not available for stored reports');
      }

      if (reportData.stored) {
        testProgress.value.logs.push('✅ Stored Security Report Displayed');
        testProgress.value.logs.push('🔄 Run new security test for updated results');
      } else {
        testProgress.value.logs.push('✅ Fresh ZAP Security Scan Complete');
        testProgress.value.logs.push('📊 Report automatically generated and displayed');
      }

    } else {
      testProgress.value.logs.push('⚠️ ZAP report not yet available, retrying...');

      // Retry after 5 seconds
      setTimeout(() => {
        fetchZapReport();
      }, 5000);
    }
  } catch (error: any) {
    console.error('Error fetching ZAP report:', error);
    if (error.response?.status === 400) {
      testProgress.value.logs.push('❌ ' + error.response.data.message);
    } else {
      testProgress.value.logs.push('❌ Failed to fetch ZAP report: ' + (error as Error).message);
    }
  }
};

const displayZapReport = (zapReport: any) => {
  testProgress.value.logs.push('📋 ZAP Security Report Generated:');

  // Display scan context information if available
  if (zapReport.scanContext) {
    if (zapReport.scanContext.targetUrl) {
      testProgress.value.logs.push(`🎯 Target: ${zapReport.scanContext.targetUrl}`);
    }

    if (zapReport.scanContext.urls && zapReport.scanContext.urls.length > 0) {
      testProgress.value.logs.push(`🔗 URLs Scanned: ${zapReport.scanContext.urls.length}`);
    }

    if (zapReport.scanContext.sites && zapReport.scanContext.sites.length > 0) {
      testProgress.value.logs.push(`📍 Sites: ${zapReport.scanContext.sites.length}`);
    }
  }

  if (zapReport.vulnerabilities) {
    testProgress.value.logs.push(`🔍 Total Vulnerabilities: ${zapReport.vulnerabilities.length}`);

    // Group by risk level
    const riskLevels = zapReport.vulnerabilities.reduce((acc: any, vuln: any) => {
      acc[vuln.risk] = (acc[vuln.risk] || 0) + 1;
      return acc;
    }, {});

    Object.entries(riskLevels).forEach(([risk, count]) => {
      const riskIcon = risk === 'High' ? '🔴' : risk === 'Medium' ? '🟡' : risk === 'Low' ? '🟢' : '⚪';
      testProgress.value.logs.push(`${riskIcon} ${risk} Risk: ${count} issues`);
    });
  }

  if (zapReport.reportUrl) {
    testProgress.value.logs.push(`📊 Full Report: ${zapReport.reportUrl}`);
  }
};





const loadStoredSecurityLogs = async () => {
  if (!selectedTestCase.value?.id) return;

  // Don't load stored logs if a test is currently running or queued
  if (testRunning.value || testProgress.value.status === 'running' || testProgress.value.status === 'queued') {
    console.log('⏸️ Skipping stored security logs loading - test is running or queued');
    return;
  }

  try {
    console.log('🔍 Loading stored security logs for test case:', selectedTestCase.value.id);

    // Check if there's a stored security report
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/security/zap-report`,
      {
        params: {
          testCaseId: selectedTestCase.value.id
        }
      }
    );

    if (response.data && response.data.success && response.data.data.stored) {
      const reportData = response.data.data;
      testProgress.value.logs.push('📊 Previous Security Scan Results:');
      testProgress.value.logs.push(`🕒 Scanned at: ${new Date(reportData.timestamp).toLocaleString()}`);
      testProgress.value.logs.push('📋 Loading stored security report...');

      // Auto-load the stored security report
      setTimeout(() => {
        fetchZapReport();
      }, 1500);

      // Update status to show there are previous results
      if (testProgress.value.status === 'idle') {
        testProgress.value.status = 'passed';
        testProgress.value.message = 'Previous security scan available';
      }
    }
  } catch (error: any) {
    // Silently handle errors for stored logs - this is optional functionality
    console.log('No stored security logs found (this is normal for new test cases)');
  }
};

const runTest = async () => {
  // Prevent double execution with debounce
  if (testRunning.value || testProgress.value.status === 'queued' || testProgress.value.status === 'running') {
    // Already running or queued a test, show a message
    const message = testProgress.value.status === 'queued'
      ? '⚠️ A test is already queued. Please wait for it to complete.'
      : '⚠️ A test is already running. Please wait for it to complete.';
    testProgress.value.logs.push(message);
    console.log('🚫 Preventing double test execution - current status:', testProgress.value.status);
    return;
  }

  console.log('🚀 Starting new security test execution');

  // Check BullMQ for active jobs before starting
  testProgress.value.logs.push('🔍 Checking for active test jobs...');
  const queueStats = await checkQueueStatus();

  if (queueStats && queueStats.active >= 10) {
    const message = `⚠️ Cannot start test: ${queueStats.active} test(s) currently running. Maximum concurrent limit (10) reached.`;
    testProgress.value.logs.push(message);
    testProgress.value.logs.push(`📊 Queue status: ${queueStats.waiting} waiting, ${queueStats.active} active`);
    testProgress.value.logs.push('🚫 Test execution blocked due to concurrent test limit.');
    return;
  }

  // Check if this specific test case is already running
  const isTestCaseRunning = await checkTestCaseRunning(selectedTestCase.value?.id ?? '');
  if (isTestCaseRunning) {
    const message = `⚠️ Cannot start test: This test case is already running from another session.`;
    testProgress.value.logs.push(message);
    testProgress.value.logs.push('🚫 Test execution blocked to prevent concurrent execution of the same test case.');
    return;
  }

  if (queueStats && queueStats.waiting > 0) {
    testProgress.value.logs.push(`📋 ${queueStats.waiting} test(s) in queue. Your test will be queued.`);
  } else {
    testProgress.value.logs.push('✅ No active jobs found. Starting test...');
  }

  // Reset state for new test run
  testRunning.value = true;
  isRunning.value = true;
  testProgress.value = {
    status: 'running',
    message: 'Starting test...',
    logs: [],
    error: '',
    currentStep: 0,
    totalSteps: automationSteps.value.length
  };

  try {
    // Fetch AgentQ API key from backend
    // testProgress.value.logs.push('🔑 Fetching AgentQ API key...');
    const fetchedApiKey = await fetchAgentQApiKey();

    if (!fetchedApiKey) {
      testRunning.value = false;
      isRunning.value = false;
      testProgress.value.status = 'failed';
      testProgress.value.error = 'No AgentQ API key available';
      testProgress.value.logs.push('❌ Cannot proceed without AgentQ API key');
      return;
    }

    // Get user JWT token from localStorage for backend authentication
    const userJwtToken = localStorage.getItem('token');
    console.log('JWT Token from localStorage:', userJwtToken ? 'Found' : 'Not found');
    console.log('JWT Token length:', userJwtToken ? userJwtToken.length : 0);
    if (userJwtToken) {
      // testProgress.value.logs.push('🔐 Sending user JWT token to WebSocket for backend authentication');
    } else {
      testProgress.value.logs.push('⚠️ No user JWT token found - test may fail');
    }

    const wsUrl = (import.meta as any).env.VITE_WEBSOCKET_SECURITY_URL || 'ws://localhost:3023';
    testProgress.value.logs.push(`🔗 Connecting to WebSocket server: ${wsUrl}`);

    // Generate unique client ID for this test run to prevent interference
    currentClientId = generateUniqueClientId();
    testProgress.value.logs.push(`🆔 Generated unique client ID: ${currentClientId}`);

    // Create a new WebSocket client for each test run with unique client ID
    testProgress.value.logs.push('🔌 Creating WebSocket client...');
    wsClient = new TestWebSocketClient(fetchedApiKey, wsUrl, userJwtToken || undefined, currentClientId || undefined);

    // Add debug logging for all WebSocket messages
    const originalOnMessage = wsClient.onMessage;
    wsClient.onMessage = (event: any) => {
      console.log('🔍 WebSocket message received:', event);
      testProgress.value.logs.push(`📨 WebSocket: ${JSON.stringify(event).substring(0, 100)}...`);
      if (originalOnMessage) {
        originalOnMessage.call(wsClient, event);
      }
    };

    // Set up event handlers
    wsClient.onConnected = () => {
      testProgress.value.logs.push('✅ Connected to WebSocket server');
      console.log('✅ WebSocket connected successfully');
    };

    wsClient.onDisconnected = () => {
      // console.log('⚠️ WebSocket disconnected, starting polling for test completion');
      // testProgress.value.logs.push('⚠️ WebSocket connection lost');
      // Start polling for test completion if test is still running
      if (testProgress.value.status === 'running' || testProgress.value.status === 'queued') {
        // testProgress.value.logs.push('⚠️ Connection lost, monitoring test completion...');
        if (currentClientId) {
          startTestCompletionPolling(currentClientId);
        }
      }
    };

    wsClient.onTestStart = (data: any) => {
      console.log('🎯 Frontend received onTestStart:', data);
      testProgress.value.status = 'running';
      testProgress.value.message = 'Test execution started';
      testProgress.value.logs.push('🚀 Test execution started');
      testProgress.value.logs.push('🔍 Starting Playwright execution with ZAP proxy...');
      testRunning.value = true;
      isRunning.value = true;
    };

    wsClient.onTestOutput = (data: any) => {
      if (data.output) {
        // Clean and add the log entry
        addLogEntry(data.output);
        console.log('Test output:', data.output);
      }
    };

    wsClient.onTestComplete = (data: any) => {
      console.log('🎯 Frontend onTestComplete handler called with data:', data);

      // Stop polling since we received the completion message
      stopTestCompletionPolling();

      // Immediately update status to reflect final result
      // Use the actual test result status (passed/failed) directly
      const finalStatus = data.status || 'failed'; // Fallback to 'failed' if status is undefined
      console.log(`🎯 Setting testProgress.status to: ${finalStatus}`);
      testProgress.value.status = finalStatus;
      testProgress.value.message = finalStatus === 'passed' ? 'Test passed successfully' : 'Test failed';

      // Add completion message
      const completionMessage = finalStatus === 'passed' ? '✅ Test passed successfully' : '❌ Test failed';
      testProgress.value.logs.push(completionMessage);

      if (data.message) {
        testProgress.value.logs.push(data.message);
      }

      // Immediately stop running state
      console.log('🎯 Setting testRunning and isRunning to false');
      testRunning.value = false;
      isRunning.value = false;

      console.log(`🎯 Test final status: ${finalStatus}, testRunning: ${testRunning.value}, testProgress.status: ${testProgress.value.status}`);

      // ALWAYS generate ZAP security report for security tests (regardless of pass/fail)
      testProgress.value.logs.push('📊 Generating ZAP security report...');
      testProgress.value.logs.push('🔒 Security analysis is independent of test result');

      // Check for ZAP report in the response
      if (data.zapReport) {
        displayZapReport(data.zapReport);
      } else {
        // Automatically fetch ZAP report after a short delay
        setTimeout(() => {
          fetchZapReport();
        }, 2000);
      }

      // Merge detailed execution logs with current logs
      setTimeout(async () => {
        await mergeDetailedLogs();
        console.log('🔄 Detailed logs merged after WebSocket completion');
      }, 1000);
    };

    wsClient.onTestError = (data: any) => {
      console.error('Test error:', data);

      // Stop polling since we received an error
      stopTestCompletionPolling();

      testProgress.value.status = 'failed';
      testProgress.value.message = 'Test execution failed';

      // Clean the error message
      const cleanedError = cleanLogForDisplay(data.message || 'Unknown error');
      addLogEntry(`❌ Error: ${cleanedError}`);

      // Explicitly set testRunning to false
      testRunning.value = false;
      isRunning.value = false;
    };

    // Add queue-related event handlers
    wsClient.onTestQueued = (data: any) => {
      console.log('Test queued:', data);
      testProgress.value.status = 'queued';
      testProgress.value.message = data.message || 'Test queued successfully';

      if (data.position && data.position > 1) {
        testProgress.value.logs.push(`📋 Test queued at position ${data.position}`);
        testProgress.value.logs.push(`⏳ ${data.queueStats?.active || 0} test(s) currently running`);
        testProgress.value.logs.push(`🔄 Estimated wait time: ${data.position * 30} seconds`);
      } else {
        testProgress.value.logs.push('📋 Test queued and will start shortly');
      }

      // Add queue monitoring
      testProgress.value.logs.push('👀 Monitoring queue status...');

      // Start polling immediately when test is queued as a backup mechanism
      console.log('🔄 Starting backup polling for queued test');
      setTimeout(() => {
        if (currentClientId) {
          startTestCompletionPolling(currentClientId);
        }
      }, 2000); // Start polling after 2 seconds to give WebSocket a chance
    };

    wsClient.onQueueStatus = (data: any) => {
      console.log('Queue status update:', data);
      if (data.status === 'active') {
        testProgress.value.status = 'running';
        testProgress.value.message = 'Test is now running';
        testProgress.value.logs.push('🚀 Your test is now starting...');
      } else if (data.status === 'waiting') {
        testProgress.value.logs.push('⏳ Still waiting in queue...');
      } else if (data.status === 'completed') {
        // Handle completion via queue status
        console.log('🎯 Test completed via queue status');
        testRunning.value = false;
        isRunning.value = false;

        // Get the actual test result status from database after a delay
        setTimeout(async () => {
          try {
            const backendUrl = (import.meta as any).env.VITE_BACKEND_URL;
            const response = await axios.get(`${backendUrl}/temp-test-results/test-case/${selectedTestCase.value?.id}`);

            if (response.data && response.data.length > 0) {
              const latestResult = response.data[0];
              const actualStatus = latestResult.status;

              // Update status with actual result
              testProgress.value.status = actualStatus;
              testProgress.value.message = actualStatus === 'passed' ? 'Test passed successfully' : 'Test failed';

              const completionMessage = actualStatus === 'passed' ? '✅ Test passed successfully' : '❌ Test failed';
              testProgress.value.logs.push(completionMessage);

              console.log(`🎯 Queue completion: actual status is ${actualStatus}`);

              // ALWAYS generate ZAP security report for security tests (regardless of pass/fail)
              testProgress.value.logs.push('📊 Auto-generating ZAP security report...');
              testProgress.value.logs.push('🔒 Security analysis is independent of test result');
              setTimeout(() => {
                fetchZapReport();
              }, 2000);

              // Merge detailed logs
              await mergeDetailedLogs();
              console.log('🔄 Detailed logs merged after queue completion');
            }
          } catch (error) {
            console.error('Failed to get actual test result:', error);
            // Fallback to completed status
            testProgress.value.status = 'completed';
            testProgress.value.message = 'Test completed';
            testProgress.value.logs.push('✅ Test completed');
          }
        }, 2000); // Longer delay to ensure backend has processed the result
      } else if (data.status === 'failed') {
        // Handle failure via queue status
        console.log('🎯 Test failed via queue status');
        testProgress.value.status = 'failed';
        testProgress.value.message = 'Test failed';
        testProgress.value.logs.push('❌ Test failed');
        if (data.message) {
          testProgress.value.logs.push(`❌ Error: ${data.message}`);
        }
        testRunning.value = false;
        isRunning.value = false;

        // Merge detailed execution logs for failed tests
        setTimeout(async () => {
          await mergeDetailedLogs();
          console.log('🔄 Detailed logs merged after queue failure');
        }, 1000);
      }
    };

    wsClient.onError = (error: any) => {
      console.error('WebSocket error:', error);

      // Don't immediately fail if it's a connection timeout - the queue system should handle this
      if (error.includes('Connection timeout') || error.includes('Unable to establish connection')) {
        testProgress.value.status = 'queued';
        testProgress.value.message = 'Connecting to test server...';
        testProgress.value.logs.push(`⏳ ${error}`);
        testProgress.value.logs.push('🔄 Retrying connection... Your test will be queued when connection is established.');
      } else {
        testProgress.value.status = 'failed';
        testProgress.value.message = 'WebSocket error';
        testProgress.value.logs.push(`❌ Error: ${error}`);

        // Only set testRunning to false for actual failures, not connection issues
        testRunning.value = false;
        isRunning.value = false;
      }
    };

    // Wait longer for WebSocket connection to establish and stabilize
    testProgress.value.logs.push('⏳ Waiting for WebSocket connection to stabilize...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Prepare test execution request
    const testExecutionRequest: TestExecutionRequest = {
      testCaseId: selectedTestCase.value?.id ?? '',
      tcId: tcId,
      projectId: projectId, // Add the projectId from the route
      steps: automationSteps.value,
      testCase: {
        title: selectedTestCase.value?.title ?? '',
        precondition: selectedTestCase.value?.precondition,
        expectation: selectedTestCase.value?.expectation
      }
    };

    // Execute the test
    wsClient.executeTest(testExecutionRequest);

  } catch (error) {
    console.error('Failed to start test execution:', error);
    testRunning.value = false;
    testProgress.value.status = 'failed';
    testProgress.value.error = error instanceof Error ? error.message : 'Unknown error';
    testProgress.value.logs.push(`❌ Failed to start test: ${testProgress.value.error}`);
  }
};

// Load test case details and API key on mount
onMounted(async () => {
  await fetchTestCaseDetails();
  await fetchAgentQApiKey();

  // Load stored security logs after test case details are loaded
  setTimeout(() => {
    loadStoredSecurityLogs();
  }, 1000);
});

// Cleanup on unmount
onUnmounted(() => {
  // Future cleanup logic for security testing
});

const editTest = () => {
  isEditing.value = true;

  // For each step, initialize UI state based on existing data
  automationSteps.value.forEach(step => {
    // Initialize with existing values or empty strings
    step.target = step.target || '';
    step.value = step.value || '';

    // Convert existing prompts to the "prompt" action type
    if (step.prompt && !step.action) {
      step.action = 'prompt';
      step.value = step.prompt;
      step.prompt = '';
    }

    // Initialize dropdown states (all closed by default)
    step.showInteractionDropdown = false;
    step.showAssertionDropdown = false;
  });
};

const saveTest = async () => {
  try {
    // Convert "prompt" actions back to the prompt field for storage
    automationSteps.value.forEach(step => {
      if (step.action === 'prompt') {
        step.prompt = step.value;
        // Don't clear the action here, as we want to keep it for display
      }
    });

    // First update the test case details if they've changed
    if (selectedTestCase.value) {
      const updatedTestCase = {
        title: selectedTestCase.value.title,
        precondition: selectedTestCase.value.precondition,
        steps: selectedTestCase.value.steps,
        expectation: selectedTestCase.value.expectation
      };

      await axios.patch(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${selectedTestCase.value.id}`,
        updatedTestCase
      );
    }

    // Then save the automation steps
    const automationData = {
      testCaseId: selectedTestCase.value?.id,
      steps: automationSteps.value,
    };

    await axios.post(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/automation`,
      automationData
    );
    console.log('Test automation steps saved successfully');

    // Update test case type to automation
    await axios.patch(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/type`,
      { testCaseType: 'automation' }
    );
    console.log('Test case type updated to automated');

    // Update automated by agentq to true
    await axios.patch(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${tcId}/automation-by-agentq`,
      { automationByAgentq: true }
    );
    console.log('Test case automation by AgentQ updated to true');

    isEditing.value = false;
  } catch (error) {
    console.error('Failed to save security test:', error);
  }
};

const cancelEdit = () => {
  isEditing.value = false;
  // Reload the original test case to discard changes
  fetchTestCaseDetails();
};

const toggleInteractionDropdown = (step: any) => {
  // Close all dropdowns first
  automationSteps.value.forEach(s => {
    if (s !== step) {
      s.showInteractionDropdown = false;
    }
    s.showAssertionDropdown = false;
  });

  // Toggle the dropdown for this step
  step.showInteractionDropdown = !step.showInteractionDropdown;
};

const toggleAssertionDropdown = (step: any) => {
  // Close all dropdowns first
  automationSteps.value.forEach(s => {
    if (s !== step) {
      s.showAssertionDropdown = false;
    }
    s.showInteractionDropdown = false;
  });

  // Toggle the dropdown for this step
  step.showAssertionDropdown = !step.showAssertionDropdown;
};

const setStepAction = (step: any, action: string) => {
  step.action = action;

  // Close dropdowns
  step.showInteractionDropdown = false;
  step.showAssertionDropdown = false;

  // Hide prompt field when setting an action
  step.showPromptField = false;

  // Set default values based on action
  if (action === 'goto') {
    step.target = '';
    step.value = '';
  } else if (action === 'click') {
    step.target = '';
    step.value = '';
  } else if (action === 'write') {
    step.target = '';
    step.value = '';
  } else if (action === 'assertText') {
    step.target = '';
    step.value = '';
  } else if (action === 'assertUrl') {
    step.target = '';
    step.value = '';
  } else if (action === 'assertVisible') {
    step.target = '';
    step.value = '';
  }
};

const togglePromptField = (step: any) => {
  // Set the action to "prompt"
  step.action = 'prompt';

  // Initialize the prompt value if it doesn't exist
  if (!step.value) {
    step.value = step.prompt || ''; // Use existing prompt if available
    step.prompt = ''; // Clear the old prompt field
  }

  // Close other dropdowns
  step.showInteractionDropdown = false;
  step.showAssertionDropdown = false;

  // Close dropdowns for other steps
  automationSteps.value.forEach(s => {
    if (s !== step) {
      s.showInteractionDropdown = false;
      s.showAssertionDropdown = false;
    }
  });
};

const clearStepAction = (step: any) => {
  // Clear the action and related fields
  step.action = '';
  step.target = '';
  step.value = '';

  // Close all dropdowns
  step.showInteractionDropdown = false;
  step.showAssertionDropdown = false;
};

// Helper function to determine log level from log message
const getLogLevel = (log: string): string => {
  const logLower = log.toLowerCase();
  if (logLower.includes('error') || logLower.includes('failed') || logLower.includes('❌')) {
    return 'error';
  } else if (logLower.includes('warn') || logLower.includes('warning') || logLower.includes('⚠️')) {
    return 'warning';
  } else if (logLower.includes('success') || logLower.includes('✅') || logLower.includes('passed')) {
    return 'success';
  } else {
    return 'info';
  }
};
</script>

<template>
  <div class="automation-container">
    <div class="automation-header">
      <h2>Security Testing (DAST) - {{ selectedTestCase?.title }}</h2>
      <div class="action-buttons">
        <template v-if="!isEditing">
          <div class="api-key-status">
            <span v-if="agentqApiKey" class="status-indicator api-connected">
              <span class="icon">🔑</span> API Key Ready
            </span>
            <span v-else class="status-indicator api-disconnected">
              <span class="icon">🔑</span> No API Key
            </span>
          </div>
          <div class="status-header" v-if="!(queueStatus.active >= 10)">
            <span v-if="testProgress.status === 'queued'" class="status-indicator queued">
              <span class="icon">📋</span> Test Queued
            </span>
            <span v-else-if="testRunning" class="status-indicator running">
              <span class="icon">🟡</span> Running Security Test...
            </span>
            <span v-else-if="testProgress.status === 'passed'" class="status-indicator passed">
              <span class="icon">✅</span> Security Test Passed
            </span>
            <span v-else-if="testProgress.status === 'failed'" class="status-indicator failed">
              <span class="icon">❌</span> Security Test Failed
            </span>
            <span v-else class="status-indicator idle">
              <span class="icon">🔒</span> Security Testing Ready
            </span>
          </div>
          <button
            class="run-button"
            @click="runTest"
            :disabled="testRunning || testProgress.status === 'queued' || testProgress.status === 'running' || queueStatus.active >= 10"
            v-if="!(queueStatus.active >= 10) && !testRunning && testProgress.status !== 'queued' && testProgress.status !== 'running'"
          >
            <span class="icon">▶️</span>
            <span>Run Security Test</span>
          </button>
          <button
            class="stop-button"
            @click="forceStopTest"
            v-if="testRunning || testProgress.status === 'queued'"
          >
            <span class="icon">⏹️</span>
            <span>{{ testProgress.status === 'queued' ? 'Cancel' : 'Stop Test' }}</span>
          </button>
          <button class="edit-button" @click="editTest" :disabled="testRunning">
            <span class="icon">✏️</span> Edit
          </button>
        </template>
        <template v-else>
          <button class="save-button" @click="saveTest">
            <span class="icon">💾</span> Save
          </button>
          <button class="cancel-button" @click="cancelEdit">
            <span class="icon">❌</span> Cancel
          </button>
        </template>
      </div>
    </div>

    <div class="automation-content" :class="{ 'editing': isEditing }">
      <div class="steps-panel">
        <div v-for="step in automationSteps" :key="step.step" class="step-item">
          <div class="step-header">Step {{ step.step }}</div>
          <div class="step-details">
            <div v-if="step.stepName" class="step-name">{{ step.stepName }}</div>

            <!-- View mode - show action details -->
            <div v-if="!isEditing" class="step-action-details">
              <div v-if="step.action === 'prompt'" class="step-action">
                <strong>Action: Prompt</strong>
                <div>Value: {{ step.value }}</div>
              </div>
              <div v-else-if="step.action === 'goto'" class="step-action">
                <strong>Action: Go to Page</strong>
                <div>URL: {{ step.target }}</div>
              </div>
              <div v-else-if="step.action === 'click'" class="step-action">
                <strong>Action: Click</strong>
                <div>Target: {{ step.target }}</div>
              </div>
              <div v-else-if="step.action === 'write'" class="step-action">
                <strong>Action: Fill</strong>
                <div>Target: {{ step.target }}</div>
                <div>Value: {{ step.value }}</div>
              </div>
              <div v-else-if="step.action === 'assertText'" class="step-action">
                <strong>Action: Assert Text</strong>
                <div>Target: {{ step.target }}</div>
                <div>Expected Text: {{ step.value }}</div>
              </div>
              <div v-else-if="step.action === 'assertUrl'" class="step-action">
                <strong>Action: Assert URL</strong>
                <div>Expected URL: {{ step.value }}</div>
              </div>
              <div v-else-if="step.action === 'assertVisible'" class="step-action">
                <strong>Action: Assert Visible</strong>
                <div>Target: {{ step.target }}</div>
              </div>
            </div>

            <!-- View mode - show message if no action is set up -->
            <div v-else-if="!isEditing" class="action-message">
              <div class="no-action-message">
                The action is not set up yet
              </div>
            </div>

            <!-- Edit mode - show action buttons and fields -->
            <div v-else class="step-actions-container">
              <div class="step-action-buttons">
                <div class="dropdown">
                  <button class="action-button prompt-button" @click="togglePromptField(step)">
                    Add Prompt
                  </button>
                </div>

                <div class="dropdown">
                  <button class="action-button interaction-button" @click="toggleInteractionDropdown(step)">
                    Add Interaction
                  </button>
                  <div v-if="step.showInteractionDropdown" class="dropdown-content">
                    <div class="dropdown-item" @click="setStepAction(step, 'goto')">Go to Page</div>
                    <div class="dropdown-item" @click="setStepAction(step, 'click')">Click</div>
                    <div class="dropdown-item" @click="setStepAction(step, 'write')">Fill</div>
                  </div>
                </div>

                <div class="dropdown">
                  <button class="action-button assertion-button" @click="toggleAssertionDropdown(step)">
                    Add Assertion
                  </button>
                  <div v-if="step.showAssertionDropdown" class="dropdown-content">
                    <div class="dropdown-item" @click="setStepAction(step, 'assertText')">Assert Text</div>
                    <div class="dropdown-item" @click="setStepAction(step, 'assertUrl')">Assert URL</div>
                    <div class="dropdown-item" @click="setStepAction(step, 'assertVisible')">Assert Visible</div>
                  </div>
                </div>
              </div>

              <!-- Prompt action field -->
              <div v-if="step.action === 'prompt'" class="action-fields">
                <div class="field-header">
                  <label>Prompt:</label>
                  <button class="close-field-button" @click="clearStepAction(step)">×</button>
                </div>
                <input v-model="step.value" type="text" placeholder="Enter prompt for AI" />
              </div>

              <!-- Show additional fields based on selected action -->
              <div v-if="step.action === 'goto'" class="action-fields">
                <div class="field-header">
                  <label>URL:</label>
                  <button class="close-field-button" @click="clearStepAction(step)">×</button>
                </div>
                <input v-model="step.target" type="text" placeholder="Enter URL" />
              </div>

              <div v-if="step.action === 'click'" class="action-fields">
                <div class="field-header">
                  <label>Target:</label>
                  <button class="close-field-button" @click="clearStepAction(step)">×</button>
                </div>
                <input v-model="step.target" type="text" placeholder="Enter selector" />
              </div>

              <div v-if="step.action === 'write'" class="action-fields">
                <div class="field-header">
                  <label>Target:</label>
                  <button class="close-field-button" @click="clearStepAction(step)">×</button>
                </div>
                <input v-model="step.target" type="text" placeholder="Enter selector" />
                <div class="field">
                  <label>Value:</label>
                  <input v-model="step.value" type="text" placeholder="Enter value" />
                </div>
              </div>

              <div v-if="step.action === 'assertText' || step.action === 'assertVisible'" class="action-fields">
                <div class="field-header">
                  <label>Target:</label>
                  <button class="close-field-button" @click="clearStepAction(step)">×</button>
                </div>
                <input v-model="step.target" type="text" placeholder="Enter selector" />
                <div class="field">
                  <label>Expected Text:</label>
                  <input v-model="step.value" type="text" placeholder="Enter expected text" />
                </div>
              </div>

              <div v-if="step.action === 'assertUrl'" class="action-fields">
                <div class="field-header">
                  <label>Expected URL:</label>
                  <button class="close-field-button" @click="clearStepAction(step)">×</button>
                </div>
                <input v-model="step.value" type="text" placeholder="Enter expected URL" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="execution-panel">
        <div class="panel-tabs">
          <button 
            class="tab-button" 
            :class="{ active: activeTab === 'logs' }"
            @click="setActiveTab('logs')"
          >
            <span class="icon">📋</span> Security Logs
          </button>
        </div>

        <div class="tab-content">
          <div v-if="activeTab === 'logs'" class="logs-container">
            <div class="logs-header">
              <h4>Security Test Execution Logs</h4>
              <p class="logs-description">
                Real-time security testing logs and DAST scan results will appear here.
              </p>
            </div>
            <div class="logs-content">
              <!-- Log entries -->
              <div v-for="(log, index) in testProgress.logs" :key="index" class="log-entry" :class="getLogLevel(log)">
                <span class="log-timestamp">{{ new Date().toLocaleTimeString() }}</span>
                <span class="log-message">{{ log }}</span>
              </div>

              <!-- Placeholder when no logs -->
              <div v-if="testProgress.logs.length === 0 && testProgress.status === 'idle'" class="no-logs">
                <p>No security test execution logs yet. Click "Run Security Test" to start DAST scanning.</p>
              </div>

              <!-- Action buttons -->
              <div class="logs-actions">
                <button
                  v-if="testProgress.logs.length > 0"
                  class="clear-logs-button"
                  @click="clearLogs()"
                >
                  <span class="icon">🗑️</span>
                  Clear Logs
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.automation-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.automation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e5e7eb;

  h2 {
    margin: 0;
    font-size: 24px;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 8px;

    &:before {
      content: '🔒';
      font-size: 28px;
    }
  }
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;

  .icon {
    font-size: 16px;
  }

  &.api-connected {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
  }

  &.api-disconnected {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
  }

  &.idle {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
  }
}

.automation-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  height: calc(100vh - 200px);
}

.steps-panel {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
}

.step-item {
  background-color: white;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.step-header {
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.step-name {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.step-action-details {
  margin-top: 8px;
  padding: 8px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #bae6fd;
}

.step-action {
  font-size: 13px;

  strong {
    color: #1e40af;
    display: block;
    margin-bottom: 4px;
  }

  div {
    color: #374151;
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.action-message {
  margin-top: 8px;
  padding: 8px;
  background-color: #fef3c7;
  border-radius: 4px;
  border: 1px solid #fbbf24;
}

.no-action-message {
  font-size: 13px;
  color: #92400e;
  font-style: italic;
}

.step-actions-container {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.step-action-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.dropdown {
  position: relative;
}

.action-button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &.prompt-button {
    background-color: #8b5cf6;
    color: white;

    &:hover {
      background-color: #7c3aed;
    }
  }

  &.interaction-button {
    background-color: #3b82f6;
    color: white;

    &:hover {
      background-color: #2563eb;
    }
  }

  &.assertion-button {
    background-color: #10b981;
    color: white;

    &:hover {
      background-color: #059669;
    }
  }
}

.dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 120px;
  margin-top: 2px;
}

.dropdown-item {
  padding: 8px 12px;
  font-size: 12px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f3f4f6;
  }
}

.action-fields {
  margin-top: 8px;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #d1d5db;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;

  label {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
  }
}

.close-field-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #374151;
  }
}

.field {
  margin-top: 8px;

  label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
  }
}

.action-fields input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;

  &:focus {
    outline: none;
    border-color: #e94560;
    box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
  }
}



.edit-button, .save-button, .cancel-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  border: none;

  .icon {
    font-size: 16px;
  }
}

.edit-button {
  background-color: #3b82f6;
  color: white;

  &:hover {
    background-color: #2563eb;
  }
}

.save-button {
  background-color: #10b981;
  color: white;

  &:hover {
    background-color: #059669;
  }
}

.cancel-button {
  background-color: #6b7280;
  color: white;

  &:hover {
    background-color: #4b5563;
  }
}

.run-button {
  background-color: #10b981;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background-color: #059669;
    transform: translateY(-1px);
  }

  &:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }

  .icon {
    font-size: 16px;
  }
}

.stop-button {
  background-color: #ef4444;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  transition: all 0.2s;

  &:hover {
    background-color: #dc2626;
    transform: translateY(-1px);
  }

  .icon {
    font-size: 16px;
  }
}

.clear-logs-button {
  background-color: #6b7280;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  border: none;
  transition: all 0.2s;

  &:hover {
    background-color: #4b5563;
  }

  .icon {
    font-size: 14px;
  }
}



.execution-panel {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-tabs {
  display: flex;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.tab-button {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;

  &:hover {
    background-color: #f3f4f6;
    color: #374151;
  }

  &.active {
    background-color: white;
    color: #e94560;
    border-bottom: 2px solid #e94560;
  }
}

.tab-content {
  flex: 1;
  overflow: hidden;
}

.logs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logs-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f8fafc;

  h4 {
    margin: 0 0 4px;
    font-size: 16px;
    color: #1f2937;
  }

  .logs-description {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
  }
}

.logs-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.no-logs {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;

  .icon {
    font-size: 48px;
    display: block;
    margin-bottom: 16px;
  }

  p {
    margin: 8px 0;
    font-size: 16px;

    &.sub-text {
      font-size: 14px;
      color: #9ca3af;
    }
  }
}

.log-entries {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.log-entry {
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-word;

  &.error {
    background-color: #fef2f2;
    color: #991b1b;
    border-left: 4px solid #ef4444;
  }

  &.warning {
    background-color: #fffbeb;
    color: #92400e;
    border-left: 4px solid #f59e0b;
  }

  &.success {
    background-color: #f0fdf4;
    color: #166534;
    border-left: 4px solid #22c55e;
  }

  &.info {
    background-color: #f8fafc;
    color: #374151;
    border-left: 4px solid #6b7280;
  }
}

.log-timestamp {
  color: #6b7280;
  font-size: 11px;
  margin-right: 8px;
}

.log-message {
  flex: 1;
}

.logs-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 1024px) {
  .automation-content {
    grid-template-columns: 1fr;
    height: auto;
  }

  .steps-panel {
    max-height: 400px;
  }

  .execution-panel {
    min-height: 500px;
  }
}
</style>
