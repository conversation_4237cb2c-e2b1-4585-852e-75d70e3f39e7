import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

export interface ZapReportData {
  html: string;
  json?: any;
  timestamp: string;
  testCaseId: string;
  clientId: string;
  vulnerabilities: ZapVulnerability[];
  summary: ZapSummary;
}

export interface ZapVulnerability {
  name: string;
  risk: 'High' | 'Medium' | 'Low' | 'Informational';
  confidence: string;
  description: string;
  solution?: string;
  reference?: string;
  instances: ZapInstance[];
}

export interface ZapInstance {
  uri: string;
  method: string;
  param?: string;
  evidence?: string;
}

export interface ZapSummary {
  totalIssues: number;
  highRisk: number;
  mediumRisk: number;
  lowRisk: number;
  informational: number;
  scanDuration?: number;
  urlsScanned?: number;
}

export class ZapService {
  private static readonly ZAP_HOST = process.env.ZAP_HOST || 'http://localhost:8080';
  private static readonly ZAP_API_KEY = process.env.ZAP_API_KEY || 'AgentqSuperAI';
  private static readonly BACKEND_URL = process.env.AGENTQ_API_URL || process.env.BACKEND_URL || 'http://localhost:3010';

  /**
   * Generate ZAP security report for a completed test
   * @param clientId - Unique client ID for isolation
   * @param testCaseId - Test case ID
   * @returns Promise<ZapReportData | null>
   */
  static async generateSecurityReport(clientId: string, testCaseId: string): Promise<ZapReportData | null> {
    try {
      console.log(`🔍 Generating ZAP security report for client: ${clientId}, test case: ${testCaseId}`);

      // Get HTML report from ZAP
      const htmlReport = await this.fetchZapHtmlReport();
      if (!htmlReport) {
        console.warn('⚠️ No ZAP HTML report available');
        return null;
      }

      // Get JSON report from ZAP for detailed analysis
      const jsonReport = await this.fetchZapJsonReport();

      // Parse vulnerabilities from the reports
      const vulnerabilities = this.parseVulnerabilities(htmlReport, jsonReport);
      const summary = this.generateSummary(vulnerabilities);

      const reportData: ZapReportData = {
        html: htmlReport,
        json: jsonReport,
        timestamp: new Date().toISOString(),
        testCaseId,
        clientId,
        vulnerabilities,
        summary
      };

      // Save report to client-isolated directory
      const reportPath = await this.saveReportToFile(reportData);
      console.log(`📊 ZAP report saved to: ${reportPath}`);

      // Store report URL in backend database
      await this.storeReportInDatabase(testCaseId, reportPath, reportData);

      return reportData;
    } catch (error) {
      console.error('❌ Error generating ZAP security report:', error);
      return null;
    }
  }

  /**
   * Fetch HTML report from ZAP
   */
  private static async fetchZapHtmlReport(): Promise<string | null> {
    try {
      const response = await axios.get(
        `${this.ZAP_HOST}/OTHER/core/other/htmlreport/?apikey=${this.ZAP_API_KEY}`,
        { timeout: 10000 }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching ZAP HTML report:', error);
      return null;
    }
  }

  /**
   * Fetch JSON report from ZAP for detailed analysis
   */
  private static async fetchZapJsonReport(): Promise<any | null> {
    try {
      const response = await axios.get(
        `${this.ZAP_HOST}/JSON/core/view/alerts/?apikey=${this.ZAP_API_KEY}`,
        { timeout: 10000 }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching ZAP JSON report:', error);
      return null;
    }
  }

  /**
   * Parse vulnerabilities from ZAP reports
   */
  private static parseVulnerabilities(htmlReport: string, jsonReport?: any): ZapVulnerability[] {
    const vulnerabilities: ZapVulnerability[] = [];

    try {
      // Parse from JSON report if available (more structured)
      if (jsonReport && jsonReport.alerts) {
        jsonReport.alerts.forEach((alert: any) => {
          const vulnerability: ZapVulnerability = {
            name: alert.name || 'Unknown Vulnerability',
            risk: this.mapRiskLevel(alert.risk),
            confidence: alert.confidence || 'Unknown',
            description: alert.description || '',
            solution: alert.solution || '',
            reference: alert.reference || '',
            instances: alert.instances?.map((instance: any) => ({
              uri: instance.uri || '',
              method: instance.method || '',
              param: instance.param || '',
              evidence: instance.evidence || ''
            })) || []
          };
          vulnerabilities.push(vulnerability);
        });
      } else {
        // Fallback to HTML parsing if JSON not available
        const htmlVulns = this.parseHtmlReport(htmlReport);
        vulnerabilities.push(...htmlVulns);
      }
    } catch (error) {
      console.error('Error parsing vulnerabilities:', error);
    }

    return vulnerabilities;
  }

  /**
   * Parse vulnerabilities from HTML report (fallback method)
   */
  private static parseHtmlReport(htmlReport: string): ZapVulnerability[] {
    const vulnerabilities: ZapVulnerability[] = [];

    try {
      // Simple regex-based parsing for HTML report
      const vulnRegex = /<h3[^>]*>([^<]+)<\/h3>[\s\S]*?Risk:\s*([^<\n]+)/gi;
      let match;

      while ((match = vulnRegex.exec(htmlReport)) !== null) {
        const name = match[1].trim();
        const risk = this.mapRiskLevel(match[2].trim());

        vulnerabilities.push({
          name,
          risk,
          confidence: 'Unknown',
          description: `Security vulnerability: ${name}`,
          instances: []
        });
      }
    } catch (error) {
      console.error('Error parsing HTML report:', error);
    }

    return vulnerabilities;
  }

  /**
   * Map ZAP risk levels to standardized format
   */
  private static mapRiskLevel(risk: string): 'High' | 'Medium' | 'Low' | 'Informational' {
    const riskLower = risk.toLowerCase();
    if (riskLower.includes('high')) return 'High';
    if (riskLower.includes('medium')) return 'Medium';
    if (riskLower.includes('low')) return 'Low';
    return 'Informational';
  }

  /**
   * Generate summary statistics from vulnerabilities
   */
  private static generateSummary(vulnerabilities: ZapVulnerability[]): ZapSummary {
    const summary: ZapSummary = {
      totalIssues: vulnerabilities.length,
      highRisk: vulnerabilities.filter(v => v.risk === 'High').length,
      mediumRisk: vulnerabilities.filter(v => v.risk === 'Medium').length,
      lowRisk: vulnerabilities.filter(v => v.risk === 'Low').length,
      informational: vulnerabilities.filter(v => v.risk === 'Informational').length
    };

    return summary;
  }

  /**
   * Save ZAP report to client-isolated file system
   */
  private static async saveReportToFile(reportData: ZapReportData): Promise<string> {
    try {
      // Use same directory structure as Playwright test results
      const outputDir = process.env.CLIENT_ID 
        ? `test-results/${process.env.CLIENT_ID}` 
        : 'test-results';

      const zapReportsDir = path.join(outputDir, 'zap-reports');
      
      // Ensure directory exists
      if (!fs.existsSync(zapReportsDir)) {
        fs.mkdirSync(zapReportsDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const reportFileName = `zap-report-${reportData.testCaseId}-${timestamp}.json`;
      const reportPath = path.join(zapReportsDir, reportFileName);

      // Save complete report data as JSON
      fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));

      // Also save HTML report separately for easy viewing
      const htmlFileName = `zap-report-${reportData.testCaseId}-${timestamp}.html`;
      const htmlPath = path.join(zapReportsDir, htmlFileName);
      fs.writeFileSync(htmlPath, reportData.html);

      console.log(`📁 ZAP report saved to: ${reportPath}`);
      return reportPath;
    } catch (error) {
      console.error('Error saving ZAP report to file:', error);
      throw error;
    }
  }

  /**
   * Store ZAP report reference in backend database
   */
  private static async storeReportInDatabase(testCaseId: string, reportPath: string, reportData: ZapReportData): Promise<void> {
    try {
      console.log(`💾 Storing ZAP report reference in database for test case: ${testCaseId}`);

      const response = await axios.post(
        `${this.BACKEND_URL}/temp-test-results/security-logs`,
        {
          testCaseId: testCaseId,
          zapReport: {
            reportPath: reportPath,
            summary: reportData.summary,
            vulnerabilities: reportData.vulnerabilities.slice(0, 10), // Store first 10 for quick access
            timestamp: reportData.timestamp,
            clientId: reportData.clientId
          }
        },
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      if (response.data.success) {
        console.log(`✅ ZAP report reference stored in database: ${response.data.data.logsSecurityUrl}`);
      } else {
        console.warn('⚠️ Failed to store ZAP report reference in database:', response.data.message);
      }
    } catch (error) {
      console.error('❌ Error storing ZAP report in database:', error);
      // Don't throw error - report is still saved locally
    }
  }

  /**
   * Clear ZAP session before starting new test
   */
  static async clearZapSession(): Promise<void> {
    try {
      console.log('🧹 Clearing ZAP session for new test...');
      
      // Clear ZAP session
      await axios.get(
        `${this.ZAP_HOST}/JSON/core/action/newSession/?apikey=${this.ZAP_API_KEY}`,
        { timeout: 5000 }
      );
      
      console.log('✅ ZAP session cleared');
    } catch (error) {
      console.warn('⚠️ Failed to clear ZAP session:', error);
      // Don't throw error - test can continue
    }
  }
}
