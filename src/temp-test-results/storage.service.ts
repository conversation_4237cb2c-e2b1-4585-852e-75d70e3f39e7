import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Storage } from '@google-cloud/storage';
import { Config } from '../config';

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private storage: Storage | null = null;
  private bucketName: string;
  private isCloudStorageEnabled: boolean;

  constructor(private configService: ConfigService) {
    this.bucketName = Config.GCP_BUCKET_NAME;
    this.isCloudStorageEnabled = process.env.ENABLE_CLOUD_STORAGE === 'true';

    // Only initialize Google Cloud Storage if enabled and credentials are available
    if (this.isCloudStorageEnabled && this.hasValidGCPCredentials()) {
      try {
        this.storage = new Storage({
          projectId: Config.GCP_PROJECT_ID,
          credentials: {
            client_email: Config.GCP_CLIENT_EMAIL,
            private_key: Config.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
          },
        });
        this.logger.log('Google Cloud Storage initialized successfully');
      } catch (error) {
        this.logger.warn('Failed to initialize Google Cloud Storage:', error);
        this.storage = null;
        this.isCloudStorageEnabled = false;
      }
    } else {
      this.logger.log('Google Cloud Storage disabled - logs will be stored in database only');
    }
  }

  private hasValidGCPCredentials(): boolean {
    return !!(
      Config.GCP_PROJECT_ID &&
      Config.GCP_CLIENT_EMAIL &&
      Config.GCP_PRIVATE_KEY &&
      Config.GCP_BUCKET_NAME &&
      Config.GCP_PROJECT_ID !== 'your_gcp_project_id' &&
      Config.GCP_CLIENT_EMAIL !== 'your_service_account@your_project.iam.gserviceaccount.com'
    );
  }

  /**
   * Upload logs to Google Cloud Storage (if enabled) or return local storage indicator
   * @param testResultId - The test result ID
   * @param logs - Array of log messages
   * @param bucketPath - The bucket path (e.g., 'temp-test-results' or 'test-results')
   * @returns Promise<string> - The GCS URL or local storage indicator
   */
  async uploadLogs(testResultId: string, logs: string[], bucketPath: string = 'temp-test-results'): Promise<string> {
    // If cloud storage is not enabled or not available, return a local storage indicator
    if (!this.isCloudStorageEnabled || !this.storage) {
      this.logger.log(`Logs stored locally in database for test result: ${testResultId} (${logs.length} entries)`);
      return `local://temp-test-results/logs/${testResultId}`;
    }

    try {
      const fileName = `temp-test-results/logs/${testResultId}/logs.json`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      const logData = {
        testResultId,
        timestamp: new Date().toISOString(),
        logs,
        metadata: {
          totalLogs: logs.length,
          uploadedAt: new Date().toISOString(),
        },
      };

      // Upload the logs as JSON
      await file.save(JSON.stringify(logData, null, 2), {
        metadata: {
          contentType: 'application/json',
        },
      });

      this.logger.log(`Logs uploaded successfully to GCS for test result: ${testResultId}`);
      return `gs://${this.bucketName}/${fileName}`;
    } catch (error) {
      this.logger.error(`Failed to upload logs to GCS for test result ${testResultId}:`, error);
      // Fallback to local storage indicator
      this.logger.log(`Falling back to local storage for test result: ${testResultId}`);
      return `local://${bucketPath}/logs/${testResultId}`;
    }
  }

  /**
   * Upload security logs (ZAP report) to Google Cloud Storage
   * @param testResultId - The test result ID
   * @param zapReport - ZAP security report data (JSON object or string)
   * @param bucketPath - The bucket path (default: 'temp-test-results')
   * @returns Promise<string> - The GCS URL or local storage indicator
   */
  async uploadSecurityLogs(testResultId: string, zapReport: any, bucketPath: string = 'temp-test-results'): Promise<string> {
    // If cloud storage is not enabled or not available, return a local storage indicator
    if (!this.isCloudStorageEnabled || !this.storage) {
      this.logger.log(`Security logs stored locally in database for test result: ${testResultId}`);
      return `local://temp-test-results/logs-dast/${testResultId}`;
    }

    try {
      const fileName = `${bucketPath}/logs-dast/${testResultId}/zap-report-${Date.now()}.json`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      // Convert zapReport to JSON string if it's an object
      const reportData = typeof zapReport === 'string' ? zapReport : JSON.stringify(zapReport, null, 2);

      await file.save(reportData, {
        metadata: {
          contentType: 'application/json',
        },
      });

      this.logger.log(`Security logs uploaded successfully to GCS for test result: ${testResultId}`);
      return `gs://${this.bucketName}/${fileName}`;
    } catch (error) {
      this.logger.error(`Failed to upload security logs to GCS for test result ${testResultId}:`, error);
      // Fallback to local storage indicator
      this.logger.log(`Falling back to local storage for security logs: ${testResultId}`);
      return `local://temp-test-results/logs-dast/${testResultId}`;
    }
  }

  /**
   * Download logs from Google Cloud Storage or indicate local storage
   * @param logsUrl - The GCS URL or local storage indicator
   * @returns Promise<string[]> - Array of log messages
   */
  async downloadLogs(logsUrl: string): Promise<string[]> {
    // If it's a local storage indicator, return empty array (logs are in database)
    if (logsUrl.startsWith('local://')) {
      this.logger.log(`Logs are stored locally in database: ${logsUrl}`);
      return []; // Logs will be retrieved from database instead
    }

    if (!this.storage) {
      this.logger.warn('Google Cloud Storage not available for downloading logs');
      return [];
    }

    try {
      // Extract file path from GCS URL
      const filePath = logsUrl.replace(`gs://${this.bucketName}/`, '');
      const file = this.storage.bucket(this.bucketName).file(filePath);

      const [contents] = await file.download();
      const logData = JSON.parse(contents.toString());

      return logData.logs || [];
    } catch (error) {
      this.logger.error(`Failed to download logs from ${logsUrl}:`, error);
      return []; // Return empty array instead of throwing error
    }
  }

  /**
   * Delete logs from Google Cloud Storage
   * @param logsUrl - The GCS URL or local storage indicator
   * @returns Promise<void>
   */
  async deleteLogs(logsUrl: string): Promise<void> {
    // If it's a local storage indicator, no need to delete from GCS
    if (logsUrl.startsWith('local://')) {
      this.logger.log(`Logs are stored locally, no GCS deletion needed: ${logsUrl}`);
      return;
    }

    if (!this.storage) {
      this.logger.warn('Google Cloud Storage not available for deleting logs');
      return;
    }

    try {
      // Extract file path from GCS URL
      const filePath = logsUrl.replace(`gs://${this.bucketName}/`, '');
      const file = this.storage.bucket(this.bucketName).file(filePath);

      // Check if file exists before trying to delete
      const [exists] = await file.exists();
      if (!exists) {
        this.logger.warn(`File does not exist in GCS, skipping deletion: ${logsUrl}`);
        return;
      }

      await file.delete();
      this.logger.log(`Logs deleted successfully: ${logsUrl}`);
    } catch (error) {
      this.logger.error(`Failed to delete logs from ${logsUrl}:`, error);
      // Don't throw error for deletion failures - log and continue
      this.logger.warn(`Continuing operation despite deletion failure for: ${logsUrl}`);
    }
  }

  /**
   * Check if the bucket exists and is accessible
   * @returns Promise<boolean>
   */
  async checkBucketAccess(): Promise<boolean> {
    if (!this.isCloudStorageEnabled || !this.storage) {
      this.logger.log('Cloud storage is disabled - using local storage');
      return false; // Not an error, just indicates local storage is being used
    }

    try {
      const bucket = this.storage.bucket(this.bucketName);
      const [exists] = await bucket.exists();

      if (!exists) {
        this.logger.warn(`Bucket ${this.bucketName} does not exist`);
        return false;
      }

      // Try to list files to check permissions
      await bucket.getFiles({ maxResults: 1 });
      this.logger.log(`Bucket ${this.bucketName} is accessible`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to access bucket ${this.bucketName}:`, error);
      return false;
    }
  }

  /**
   * Upload video to Google Cloud Storage
   * @param testResultId - The test result ID
   * @param videoBuffer - The video file buffer
   * @param contentType - The video content type (e.g., 'video/webm')
   * @returns Promise<string> - The GCS URL
   */
  async uploadVideo(testResultId: string, videoBuffer: Buffer, contentType: string = 'video/webm'): Promise<string> {
    try {
      const fileName = `temp-test-results/videos/${testResultId}/video.webm`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      // Upload the video
      await file.save(videoBuffer, {
        metadata: {
          contentType: contentType,
        },
      });

      this.logger.log(`Video uploaded successfully for test result: ${testResultId}`);
      return `gs://${this.bucketName}/${fileName}`;
    } catch (error) {
      this.logger.error(`Failed to upload video for test result ${testResultId}:`, error);
      throw new Error(`Failed to upload video: ${error}`);
    }
  }

  /**
   * Get a signed URL for a file in Google Cloud Storage
   * @param gcsUrl - The GCS URL (gs://bucket-name/path/to/file)
   * @returns Promise<string> - The signed URL for direct access
   */
  async getSignedUrl(gcsUrl: string): Promise<string> {
    try {
      // Extract file path from GCS URL
      const filePath = gcsUrl.replace(`gs://${this.bucketName}/`, '');
      const file = this.storage.bucket(this.bucketName).file(filePath);

      // Generate a signed URL that expires in 1 hour
      const [signedUrl] = await file.getSignedUrl({
        version: 'v4',
        action: 'read',
        expires: Date.now() + 60 * 60 * 1000, // 1 hour
      });

      return signedUrl;
    } catch (error) {
      this.logger.error(`Failed to generate signed URL for ${gcsUrl}:`, error);
      throw new Error(`Failed to generate signed URL: ${error}`);
    }
  }
}
